import { useState, useEffect } from "react";
import { web3Utils, isValidAddress } from "../utils/web3.js";
import {
  ERC721_COLLECTION_ABI,
  ERC1155_COLLECTION_ABI,
} from "../contracts/config.js";

const MintNFT = ({ account, collections }) => {
  const [selectedCollection, setSelectedCollection] = useState("");
  const [mintAmount, setMintAmount] = useState(1);
  const [recipient, setRecipient] = useState("");
  const [isMinting, setIsMinting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [collectionInfo, setCollectionInfo] = useState(null);

  useEffect(() => {
    if (account) {
      setRecipient(account);
    }
  }, [account]);

  useEffect(() => {
    if (selectedCollection) {
      loadCollectionInfo();
    }
  }, [selectedCollection]);

  const loadCollectionInfo = async () => {
    if (!selectedCollection) return;

    try {
      const collection = collections.find(
        (c) => c.address === selectedCollection
      );
      if (!collection) return;

      const abi =
        collection.type === "ERC721"
          ? ERC721_COLLECTION_ABI
          : ERC1155_COLLECTION_ABI;
      const contract = web3Utils.getContract(selectedCollection, abi);

      const [name, symbol, mintPrice] = await Promise.all([
        contract.name(),
        contract.symbol(),
        contract.getMintPrice(),
      ]);

      setCollectionInfo({
        name,
        symbol,
        mintPrice: web3Utils.formatEther(mintPrice),
        type: collection.type,
      });
    } catch (error) {
      console.error("Error loading collection info:", error);
    }
  };

  const handleMint = async (isBatch = false) => {
    if (!account) {
      setError("Please connect your wallet first");
      return;
    }

    if (!selectedCollection) {
      setError("Please select a collection");
      return;
    }

    if (!recipient || !isValidAddress(recipient)) {
      setError("Please enter a valid recipient address");
      return;
    }

    if (isBatch && mintAmount < 2) {
      setError("Batch mint requires at least 2 NFTs");
      return;
    }

    setIsMinting(true);
    setError(null);
    setSuccess(null);

    try {
      const collection = collections.find(
        (c) => c.address === selectedCollection
      );
      const abi =
        collection.type === "ERC721"
          ? ERC721_COLLECTION_ABI
          : ERC1155_COLLECTION_ABI;
      const contract = web3Utils.getContract(selectedCollection, abi);

      // Check collection status before minting
      console.log("Checking collection status...");

      // First, update the mint stage
      console.log("Updating mint stage...");
      await contract.updateMintStage();

      const currentStage = await contract.getCurrentStage();
      const mintStartTime = await contract.getMintStartTime();
      const allowlistStageEnd = await contract.getAllowlistStageEnd();
      const currentTime = Math.floor(Date.now() / 1000);

      console.log("Current stage:", currentStage);
      console.log("Mint start time:", mintStartTime.toString());
      console.log("Allowlist stage end:", allowlistStageEnd.toString());
      console.log("Current time:", currentTime);
      console.log(
        "Time difference:",
        currentTime - Number(mintStartTime.toString())
      );

      if (currentTime < Number(mintStartTime.toString())) {
        throw new Error(
          `Minting not started yet. Starts at ${new Date(
            Number(mintStartTime.toString()) * 1000
          ).toLocaleString()}`
        );
      }

      if (currentStage === 0) {
        // INACTIVE
        throw new Error("Minting is not active for this collection");
      }

      const mintPrice = await contract.getMintPrice();
      const totalPrice = mintPrice * BigInt(isBatch ? mintAmount : 1);

      console.log("Mint price:", mintPrice.toString());
      console.log("Total price:", totalPrice.toString());

      let tx;
      if (collection.type === "ERC721") {
        if (isBatch) {
          tx = await contract.batchMintERC721(recipient, mintAmount, {
            value: totalPrice,
          });
        } else {
          tx = await contract.mint(recipient, {
            value: totalPrice,
          });
        }
      } else {
        // ERC1155
        if (isBatch) {
          tx = await contract.batchMintERC1155(recipient, mintAmount, {
            value: totalPrice,
          });
        } else {
          tx = await contract.mint(recipient, 1, {
            value: totalPrice,
          });
        }
      }

      setSuccess("Transaction submitted! Waiting for confirmation...");

      const receipt = await tx.wait();

      setSuccess(
        `Successfully minted ${isBatch ? mintAmount : 1} NFT${
          isBatch ? "s" : ""
        }! 
         Transaction: ${receipt.hash}`
      );
    } catch (error) {
      console.error("Error minting NFT:", error);
      setError(error.message || "Failed to mint NFT");
    } finally {
      setIsMinting(false);
    }
  };

  return (
    <div className="mint-nft">
      <h2>Mint NFT</h2>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      <div className="mint-form">
        <div className="form-group">
          <label>Select Collection:</label>
          <select
            value={selectedCollection}
            onChange={(e) => setSelectedCollection(e.target.value)}
            disabled={isMinting}
          >
            <option value="">Choose a collection...</option>
            {collections.map((collection) => (
              <option key={collection.address} value={collection.address}>
                {collection.name} ({collection.symbol}) - {collection.type}
              </option>
            ))}
          </select>
        </div>

        {collectionInfo && (
          <div className="collection-info">
            <h3>
              {collectionInfo.name} ({collectionInfo.symbol})
            </h3>
            <p>
              <strong>Type:</strong> {collectionInfo.type}
            </p>
            <p>
              <strong>Mint Price:</strong> {collectionInfo.mintPrice} ETH
            </p>
          </div>
        )}

        <div className="form-group">
          <label>Recipient Address:</label>
          <input
            type="text"
            value={recipient}
            onChange={(e) => setRecipient(e.target.value)}
            placeholder="0x..."
            disabled={isMinting}
          />
        </div>

        <div className="form-group">
          <label>Amount (for batch minting):</label>
          <input
            type="number"
            value={mintAmount}
            onChange={(e) => setMintAmount(parseInt(e.target.value) || 1)}
            min="1"
            max="50"
            disabled={isMinting}
          />
        </div>

        {collectionInfo && (
          <div className="price-info">
            <p>
              <strong>Single Mint Cost:</strong> {collectionInfo.mintPrice} ETH
            </p>
            <p>
              <strong>Batch Mint Cost ({mintAmount} NFTs):</strong>{" "}
              {(parseFloat(collectionInfo.mintPrice) * mintAmount).toFixed(6)}{" "}
              ETH
            </p>
          </div>
        )}

        <div className="mint-buttons">
          <button
            onClick={() => handleMint(false)}
            disabled={isMinting || !selectedCollection || !account}
            className="mint-button single"
          >
            {isMinting ? "Minting..." : "Mint Single NFT"}
          </button>

          <button
            onClick={() => handleMint(true)}
            disabled={
              isMinting || !selectedCollection || !account || mintAmount < 2
            }
            className="mint-button batch"
          >
            {isMinting ? "Minting..." : `Batch Mint ${mintAmount} NFTs`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MintNFT;
