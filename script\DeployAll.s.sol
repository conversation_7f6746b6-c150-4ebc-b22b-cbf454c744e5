// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Script, console2} from "forge-std/Script.sol";
import {NFTExchange} from "../src/contracts/core/NFTExchange.sol";
import {CollectionFactory} from "../src/contracts/core/Collection.sol";

contract DeployAll is Script {
    function run() external {
        // Get marketplace wallet from environment variable or use deployer as default
        address marketplaceWallet = vm.envOr("MARKETPLACE_WALLET", msg.sender);

        console2.log("=== Deploying All Contracts ===");
        console2.log("Marketplace wallet:", marketplaceWallet);
        console2.log("Deployer:", msg.sender);

        vm.startBroadcast();

        // Deploy NFT Exchange
        console2.log("\n1. Deploying NFTExchange...");
        NFTExchange exchange = new NFTExchange(marketplaceWallet);
        console2.log("   NFTExchange deployed at:", address(exchange));

        // Deploy Collection Factory
        console2.log("\n2. Deploying CollectionFactory...");
        CollectionFactory collectionFactory = new CollectionFactory();
        console2.log("   CollectionFactory deployed at:", address(collectionFactory));

        vm.stopBroadcast();

        // Save all contract addresses to a single JSON file
        console2.log("\n3. Saving contract addresses...");
        
        string memory json = "deployment";
        vm.serializeAddress(json, "exchange", address(exchange));
        vm.serializeAddress(json, "collectionFactory", address(collectionFactory));
        string memory finalJson = vm.serializeAddress(json, "marketplaceWallet", marketplaceWallet);

        // Write to main addresses file
        vm.writeFile("deployments/addresses.json", finalJson);
        console2.log("   All addresses saved to deployments/addresses.json");

        // Also create individual files for backward compatibility
        string memory exchangeJson = vm.serializeAddress("exchange", "exchange", address(exchange));
        vm.writeFile("deployments/exchange.json", exchangeJson);

        string memory collectionJson = vm.serializeAddress("collection", "collectionFactory", address(collectionFactory));
        vm.writeFile("deployments/collection.json", collectionJson);

        console2.log("\n=== Deployment Summary ===");
        console2.log("NFTExchange:", address(exchange));
        console2.log("CollectionFactory:", address(collectionFactory));
        console2.log("MarketplaceWallet:", marketplaceWallet);
        console2.log("Gas used for NFTExchange: ~4.2M");
        console2.log("Gas used for CollectionFactory: ~4.6M");
        console2.log("Total estimated gas: ~8.8M");
    }
}
