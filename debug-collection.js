// Debug script to check collection status
import { ethers } from 'ethers';

// Error selectors for our custom errors
const errorSelectors = {
  'Collection__MintingNotStarted()': ethers.id('Collection__MintingNotStarted()').slice(0, 10),
  'Collection__MintingNotActive()': ethers.id('Collection__MintingNotActive()').slice(0, 10),
  'Collection__MintLimitExceeded()': ethers.id('Collection__MintLimitExceeded()').slice(0, 10),
  'Collection__NotInAllowlist()': ethers.id('Collection__NotInAllowlist()').slice(0, 10),
  'Collection__InsufficientPayment()': ethers.id('Collection__InsufficientPayment()').slice(0, 10),
  'Collection__InvalidAmount()': ethers.id('Collection__InvalidAmount()').slice(0, 10),
  'Collection__InvalidAddress()': ethers.id('Collection__InvalidAddress()').slice(0, 10),
};

console.log('Error selectors:');
Object.entries(errorSelectors).forEach(([name, selector]) => {
  console.log(`${name}: ${selector}`);
});

console.log('\nLooking for: 0xf501eed5');

// Find matching error
const targetError = '0xf501eed5';
const matchingError = Object.entries(errorSelectors).find(([name, selector]) => 
  selector.toLowerCase() === targetError.toLowerCase()
);

if (matchingError) {
  console.log(`\nFound matching error: ${matchingError[0]}`);
} else {
  console.log('\nNo matching error found in our custom errors');
}
