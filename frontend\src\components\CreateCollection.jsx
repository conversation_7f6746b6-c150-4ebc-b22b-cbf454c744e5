import { useState } from "react";
import { web3Utils } from "../utils/web3.js";
import {
  CONTRACT_ADDRESSES,
  COLLECTION_FACTORY_ABI,
} from "../contracts/config.js";

const CreateCollection = ({ account, onCollectionCreated }) => {
  const [formData, setFormData] = useState({
    name: "",
    symbol: "",
    description: "",
    tokenURI: "",
    mintPrice: "0.01",
    maxSupply: "1000",
    mintLimitPerWallet: "10",
    mintStartTime: Math.floor(Date.now() / 1000 - 60).toString(), // Start 1 minute ago
    royaltyFee: "250", // 2.5% in basis points
    collectionType: "ERC721",
  });

  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    const errors = [];

    if (!formData.name.trim()) errors.push("Collection name is required");
    if (!formData.symbol.trim()) errors.push("Symbol is required");
    if (!formData.description.trim()) errors.push("Description is required");
    if (!formData.tokenURI.trim()) errors.push("Token URI is required");
    if (parseFloat(formData.mintPrice) < 0)
      errors.push("Mint price must be non-negative");
    if (parseInt(formData.maxSupply) <= 0)
      errors.push("Max supply must be greater than 0");
    if (parseInt(formData.mintLimitPerWallet) <= 0)
      errors.push("Mint limit must be greater than 0");
    if (
      parseInt(formData.royaltyFee) < 0 ||
      parseInt(formData.royaltyFee) > 1000
    ) {
      errors.push("Royalty fee must be between 0-10%");
    }

    return errors;
  };

  const createCollection = async (e) => {
    e.preventDefault();

    if (!account) {
      setError("Please connect your wallet first");
      return;
    }

    if (!CONTRACT_ADDRESSES.COLLECTION_FACTORY) {
      setError("Collection Factory contract not deployed");
      return;
    }

    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors.join(", "));
      return;
    }

    setIsCreating(true);
    setError(null);
    setSuccess(null);

    try {
      const contract = web3Utils.getContract(
        CONTRACT_ADDRESSES.COLLECTION_FACTORY,
        COLLECTION_FACTORY_ABI,
        web3Utils.signer
      );

      const params = {
        name: formData.name,
        symbol: formData.symbol,
        owner: account,
        description: formData.description,
        mintPrice: web3Utils.parseEther(formData.mintPrice),
        royaltyFee: BigInt(formData.royaltyFee),
        maxSupply: BigInt(formData.maxSupply),
        mintLimitPerWallet: BigInt(formData.mintLimitPerWallet),
        mintStartTime: BigInt(formData.mintStartTime),
        allowlistMintPrice: web3Utils.parseEther(formData.mintPrice), // Use same price for now
        publicMintPrice: web3Utils.parseEther(formData.mintPrice), // Use same price for now
        allowlistStageDuration: BigInt(86400), // 1 day in seconds
        tokenURI: formData.tokenURI,
      };

      // Check if contract exists at the address
      if (!web3Utils.provider) {
        throw new Error("Provider not initialized");
      }

      const contractCode = await web3Utils.provider.getCode(
        CONTRACT_ADDRESSES.COLLECTION_FACTORY
      );
      console.log("Contract code length:", contractCode.length);
      if (contractCode === "0x") {
        throw new Error(
          "Collection Factory contract not found at the specified address"
        );
      }

      let tx;

      if (formData.collectionType === "ERC721") {
        tx = await contract.createERC721Collection(params);
      } else {
        tx = await contract.createERC1155Collection(params);
      }

      console.log("Transaction sent:", tx);
      setSuccess(
        "Transaction submitted! Waiting for confirmation..." + tx.hash
      );

      const receipt = await tx.wait();
      console.log("Transaction receipt:", receipt);
      console.log("Receipt logs:", receipt.logs);
      console.log("Transaction status:", receipt.status);
      console.log("Gas used:", receipt.gasUsed?.toString());
      console.log("Gas limit:", tx.gasLimit?.toString());
      console.log("Transaction hash:", receipt.hash);

      // Find the collection created event
      let collectionAddress = null;
      const eventName = `${formData.collectionType}CollectionCreated`;

      // Log all receipt logs for debugging
      receipt.logs.forEach((log, index) => {
        console.log(`Log ${index}:`, log);
        console.log(`Log ${index} topics:`, log.topics);
        console.log(`Log ${index} data:`, log.data);
      });

      for (const log of receipt.logs) {
        try {
          const parsed = contract.interface.parseLog(log);
          console.log("Parsed event:", parsed);

          if (parsed.name === eventName) {
            collectionAddress = parsed.args.collectionAddress;
            console.log("Found collection address:", collectionAddress);
            break;
          }
        } catch (error) {
          console.log("Could not parse log:", error);
          // Skip logs that can't be parsed by this contract
          continue;
        }
      }

      // If we couldn't find the event, try alternative methods
      if (!collectionAddress && receipt.logs.length > 0) {
        console.log("Trying alternative method to get collection address...");

        // Try to call the contract function again to get the return value
        // Since the function returns the collection address, we can try to simulate the call
        try {
          // Use staticCall to get the return value without sending a transaction
          const result = await contract.createERC721Collection.staticCall(
            params
          );
          console.log("Static call result:", result);
          collectionAddress = result;
        } catch (staticCallError) {
          console.log("Static call failed:", staticCallError);

          // Last resort: try to parse logs manually using ethers utils
          for (const log of receipt.logs) {
            console.log("Trying to parse log manually:", log);

            // Check if this log has the right number of topics for our event
            if (log.topics && log.topics.length === 3) {
              // Our event has 2 indexed parameters + event signature = 3 topics
              console.log("Log has 3 topics, might be our event");

              // The second topic should be the collection address (first indexed parameter)
              const potentialAddress = log.topics[1];
              if (potentialAddress && potentialAddress.length === 66) {
                // Convert from bytes32 to address (remove padding)
                collectionAddress = "0x" + potentialAddress.slice(-40);
                console.log(
                  "Extracted potential collection address:",
                  collectionAddress
                );
                break;
              }
            }
          }
        }
      }

      if (collectionAddress) {
        setSuccess(
          `Collection created successfully! Address: ${collectionAddress}`
        );
        onCollectionCreated?.({
          address: collectionAddress,
          type: formData.collectionType,
          name: formData.name,
          symbol: formData.symbol,
        });

        // Reset form
        setFormData({
          name: "",
          symbol: "",
          description: "",
          tokenURI: "",
          mintPrice: "0.01",
          maxSupply: "1000",
          mintLimitPerWallet: "10",
          mintStartTime: Math.floor(Date.now() / 1000 - 60).toString(), // Start 1 minute ago
          royaltyFee: "250",
          collectionType: "ERC721",
        });
      } else {
        setError(
          "Collection created but could not find collection address in events. Check console for details."
        );
      }
    } catch (error) {
      console.error("Error creating collection:", error);
      setError(error.message || "Failed to create collection");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="create-collection">
      <h2>Create New Collection</h2>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      <form onSubmit={createCollection} className="collection-form">
        <div className="form-group">
          <label>Collection Type:</label>
          <select
            name="collectionType"
            value={formData.collectionType}
            onChange={handleInputChange}
            disabled={isCreating}
          >
            <option value="ERC721">ERC721 (Unique NFTs)</option>
            <option value="ERC1155">ERC1155 (Multi-edition NFTs)</option>
          </select>
        </div>

        <div className="form-group">
          <label>Collection Name:</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="My Awesome Collection"
            disabled={isCreating}
            required
          />
        </div>

        <div className="form-group">
          <label>Symbol:</label>
          <input
            type="text"
            name="symbol"
            value={formData.symbol}
            onChange={handleInputChange}
            placeholder="MAC"
            disabled={isCreating}
            required
          />
        </div>

        <div className="form-group">
          <label>Description:</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Describe your collection..."
            disabled={isCreating}
            required
          />
        </div>

        <div className="form-group">
          <label>Base Token URI:</label>
          <input
            type="url"
            name="tokenURI"
            value={formData.tokenURI}
            onChange={handleInputChange}
            placeholder="https://api.example.com/metadata/"
            disabled={isCreating}
            required
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Mint Price (ETH):</label>
            <input
              type="number"
              name="mintPrice"
              value={formData.mintPrice}
              onChange={handleInputChange}
              step="0.001"
              min="0"
              disabled={isCreating}
              required
            />
          </div>

          <div className="form-group">
            <label>Max Supply:</label>
            <input
              type="number"
              name="maxSupply"
              value={formData.maxSupply}
              onChange={handleInputChange}
              min="1"
              disabled={isCreating}
              required
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label>Mint Limit Per Wallet:</label>
            <input
              type="number"
              name="mintLimitPerWallet"
              value={formData.mintLimitPerWallet}
              onChange={handleInputChange}
              min="1"
              disabled={isCreating}
              required
            />
          </div>

          <div className="form-group">
            <label>Royalty Fee (%):</label>
            <input
              type="number"
              name="royaltyFee"
              value={formData.royaltyFee / 100}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  royaltyFee: (parseFloat(e.target.value) * 100).toString(),
                }))
              }
              step="0.1"
              min="0"
              max="10"
              disabled={isCreating}
              required
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isCreating || !account}
          className="create-button"
        >
          {isCreating ? "Creating..." : "Create Collection"}
        </button>
      </form>
    </div>
  );
};

export default CreateCollection;
