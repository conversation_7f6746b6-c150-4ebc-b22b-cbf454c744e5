# Foundry Marketplace Deployment Commands

# Start local anvil node
start-anvil:; @anvil --host 0.0.0.0 --port 8545

# Complete setup: start anvil in background and deploy all contracts
setup-local:; @echo "🚀 Starting complete local setup..." && \
	(anvil --host 0.0.0.0 --port 8545 > /dev/null 2>&1 &) && \
	echo "⏳ Waiting for anvil to start..." && \
	sleep 3 && \
	echo "📦 Deploying all contracts..." && \
	make deploy-all-local && \
	echo "✅ Setup complete! Anvil is running on http://localhost:8545"

# Deploy all contracts to local network (anvil) - RECOMMENDED
deploy-all-local:; @forge script script/DeployAll.s.sol:DeployAll --fork-url http://localhost:8545 --account AccTest --broadcast -vvvv && node script/update-frontend-config.js

# Deploy all contracts with custom account (no network specified)
deploy-all:; @forge script script/DeployAll.s.sol:DeployAll --account AccTest --broadcast -vvvv && node script/update-frontend-config.js

# Update frontend config with deployed contract addresses
update-frontend:; @node script/update-frontend-config.js

# Individual contract deployments (legacy)
deploy-local:; @forge script script/DeployNFTExchange.s.sol:DeployNFTExchange --fork-url http://localhost:8545 --account AccTest --broadcast -vvvv
deploy-contract:; @forge script script/DeployNFTExchange.s.sol:DeployNFTExchange --account AccTest --broadcast -vvvv
deploy-collection:; @forge script script/DeployCollection.s.sol:DeployCollection --account AccTest --broadcast -vvvv
deploy-collection-local:; @forge script script/DeployCollection.s.sol:DeployCollection --fork-url http://localhost:8545 --account AccTest --broadcast -vvvv
